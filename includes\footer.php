  

    <!-- footer section start  -->
    <div class="footer-section">
        <div class="container">
          
            <div class="row pb-50 g-4 justify-content-between">
                <div class="col-lg-2 col-md-6 col-sm-6 col">
                    <div class="footer-widget">
                        <div class="widget-title">
                            <h4>Quick Links</h4>
                        </div>
                        <div class="menu-container">
                            <ul class="widget-list">
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>
                                        Home</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>
                                        About Us</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>
                                        Services</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>
                                        Contacts</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>
                                        News & Blogs</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 col-sm-6">
                    <div class="footer-widget">
                        <div class="widget-title">
                            <h4>Services</h4>
                        </div>
                        <div class="menu-container">
                            <ul class="widget-list">
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>

                                        Security Guards</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>

                                        Facility Management</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>

                                        Housekeeping</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>

                                        Bouncers & Bodyguards</a></li>
                                <li><a href="#">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M6.70861 12.2682H8.14828C8.32736 12.2682 8.49886 12.197 8.62545 12.0704L13.2192 7.4767C13.2818 7.41408 13.3315 7.33973 13.3654 7.25791C13.3993 7.17609 13.4168 7.08839 13.4168 6.99982C13.4168 6.91125 13.3993 6.82355 13.3654 6.74173C13.3315 6.65991 13.2818 6.58556 13.2192 6.52294L8.62545 1.9292C8.56282 1.86649 8.48843 1.81678 8.40655 1.78289C8.32467 1.74901 8.2369 1.73162 8.14828 1.73174L6.70861 1.73145C6.57529 1.73165 6.44502 1.77133 6.33422 1.84548C6.22343 1.91964 6.13708 2.02495 6.08607 2.14812C6.03506 2.2713 6.02167 2.40682 6.0476 2.5376C6.07352 2.66838 6.1376 2.78854 6.23174 2.88295L10.2842 6.93536C10.3012 6.95247 10.3108 6.97565 10.3108 6.99982C10.3108 7.02399 10.3012 7.04717 10.2842 7.06428L6.23174 11.1167C6.13745 11.211 6.07325 11.3312 6.04725 11.462C6.02124 11.5929 6.0346 11.7285 6.08564 11.8517C6.13667 11.9749 6.22309 12.0803 6.33398 12.1544C6.44486 12.2285 6.57523 12.2681 6.70861 12.2682ZM1.25795 12.2682H2.69761C2.7862 12.2682 2.87391 12.2507 2.95574 12.2168C3.03757 12.1829 3.1119 12.1331 3.17449 12.0704L7.76824 7.4767C7.83087 7.41408 7.88055 7.33973 7.91445 7.25791C7.94835 7.17609 7.9658 7.08839 7.9658 6.99982C7.9658 6.91125 7.94835 6.82355 7.91445 6.74173C7.88055 6.65991 7.83087 6.58556 7.76824 6.52294L3.17449 1.9292C3.11192 1.8665 3.03758 1.81679 2.95575 1.7829C2.87391 1.74902 2.78619 1.73163 2.69761 1.73174L1.25795 1.73145C1.12454 1.73145 0.994128 1.77101 0.8832 1.84512C0.772271 1.91923 0.685806 2.02457 0.634735 2.14781C0.583664 2.27106 0.57028 2.40668 0.596275 2.53753C0.62227 2.66838 0.686478 2.78858 0.78078 2.88295L4.8332 6.93536C4.85026 6.95247 4.85984 6.97565 4.85984 6.99982C4.85984 7.02399 4.85026 7.04717 4.8332 7.06428L0.78078 11.1167C0.686478 11.2111 0.62227 11.3313 0.596275 11.4621C0.57028 11.593 0.583664 11.7286 0.634735 11.8518C0.685806 11.9751 0.772271 12.0804 0.8832 12.1545C0.994128 12.2286 1.12454 12.2682 1.25795 12.2682Z"
                                                fill="#F84E1D" />
                                        </svg>

                                        Payroll Management</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="footer-widget">
                        <div class="widget-title">
                            <h4>Contact</h4>
                        </div>
                        <div class="contact-information">
                            <span>Email Us:</span>
                            <ul>
                                <li>
                                    <div class="icon">
                                        <svg width="28" height="28" viewBox="0 0 28 28" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M25.5391 4.15625H2.46094C1.10124 4.15625 0 5.28192 0 6.65625V21.6562C0 23.0387 1.10934 24.1562 2.46094 24.1562H25.5391C26.8874 24.1562 28 23.0435 28 21.6562V6.65625C28 5.28436 26.9031 4.15625 25.5391 4.15625ZM25.1944 5.82292L15.7402 15.3766C15.2753 15.8489 14.6573 16.1089 14 16.1089C13.3427 16.1089 12.7247 15.8488 12.2583 15.3751L2.80558 5.82292H25.1944ZM1.64062 21.317V6.99653L8.7302 14.1607L1.64062 21.317ZM2.80662 22.4896L9.89341 15.3361L11.0998 16.5551C11.8745 17.3421 12.9044 17.7755 14 17.7755C15.0956 17.7755 16.1255 17.3421 16.8987 16.5567L18.1066 15.3361L25.1934 22.4896H2.80662ZM26.3594 21.317L19.2698 14.1607L26.3594 6.99653V21.317Z"
                                                fill="#F84E1D" />
                                        </svg>
                                    </div>
                                </li>
                                <li>
                                    <div class="content">
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="contact-information">
                            <span>Call Us:</span>
                            <ul>
                                <li>
                                    <div class="icon">
                                        <svg width="28" height="28" viewBox="0 0 28 28" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M22.1288 17.345C21.5556 16.7481 20.8642 16.429 20.1314 16.429C19.4045 16.429 18.7072 16.7422 18.1103 17.3391L16.2429 19.2006C16.0892 19.1179 15.9356 19.041 15.7879 18.9642C15.5751 18.8579 15.3742 18.7574 15.2028 18.651C13.4536 17.54 11.8639 16.0922 10.3392 14.2188C9.60051 13.2851 9.10411 12.4991 8.74362 11.7013C9.22821 11.2581 9.67734 10.7972 10.1146 10.3539C10.2801 10.1885 10.4456 10.0171 10.6111 9.85163C11.8521 8.61062 11.8521 7.00321 10.6111 5.7622L8.99774 4.14888C8.81454 3.96568 8.62543 3.77657 8.44814 3.58747C8.09357 3.22107 7.72126 2.84286 7.33714 2.48828C6.76391 1.92096 6.0784 1.61957 5.35743 1.61957C4.63646 1.61957 3.93912 1.92096 3.34817 2.48828L3.33635 2.5001L1.32708 4.52709C0.570656 5.28352 0.139256 6.20542 0.0447028 7.27505C-0.0971274 9.00065 0.411098 10.6081 0.801131 11.66C1.75848 14.2425 3.18861 16.6358 5.32197 19.2006C7.91037 22.2913 11.0247 24.732 14.5823 26.4517C15.9415 27.0958 17.7558 27.8582 19.7827 27.9882C19.9068 27.9941 20.0369 28 20.155 28C21.5202 28 22.6666 27.5095 23.5649 26.5344C23.5708 26.5226 23.5826 26.5167 23.5885 26.5049C23.8958 26.1326 24.2504 25.7957 24.6227 25.4352C24.8768 25.1929 25.1368 24.9388 25.3909 24.6729C25.976 24.0642 26.2833 23.355 26.2833 22.6282C26.2833 21.8954 25.9701 21.1921 25.3732 20.6012L22.1288 17.345ZM24.2445 23.5678C24.2386 23.5678 24.2386 23.5737 24.2445 23.5678C24.014 23.816 23.7776 24.0406 23.5235 24.2888C23.1394 24.6552 22.7494 25.0393 22.383 25.4707C21.7861 26.1089 21.0829 26.4103 20.161 26.4103C20.0723 26.4103 19.9778 26.4103 19.8891 26.4044C18.134 26.2921 16.5029 25.6066 15.2796 25.0216C11.9348 23.4023 8.99774 21.1035 6.55707 18.1901C4.5419 15.7612 3.19452 13.5156 2.30217 11.1045C1.75258 9.63298 1.55165 8.48652 1.64029 7.40506C1.69939 6.71364 1.96532 6.14041 2.45582 5.64991L4.47099 3.63474C4.76056 3.3629 5.06786 3.21516 5.36925 3.21516C5.74155 3.21516 6.04294 3.43973 6.23205 3.62883L6.24977 3.64656C6.61026 3.98341 6.95302 4.33207 7.3135 4.70438C7.4967 4.89349 7.68581 5.08259 7.87491 5.27761L9.48823 6.89093C10.1146 7.51734 10.1146 8.09648 9.48823 8.7229C9.31685 8.89428 9.15138 9.06566 8.98001 9.23113C8.4836 9.73935 8.01083 10.2121 7.4967 10.6731C7.48488 10.6849 7.47306 10.6908 7.46715 10.7026C6.95893 11.2108 7.05348 11.7072 7.15985 12.0441L7.17758 12.0973C7.59716 13.1137 8.18812 14.0711 9.08638 15.2116L9.09229 15.2175C10.7233 17.2268 12.443 18.7928 14.34 19.9925C14.5823 20.1461 14.8305 20.2702 15.0669 20.3884C15.2796 20.4948 15.4806 20.5953 15.6519 20.7016C15.6756 20.7135 15.6992 20.7312 15.7229 20.743C15.9238 20.8435 16.1129 20.8908 16.3079 20.8908C16.7984 20.8908 17.1057 20.5835 17.2062 20.483L19.2272 18.4619C19.4282 18.261 19.7473 18.0187 20.1196 18.0187C20.486 18.0187 20.7874 18.2492 20.9706 18.4501L20.9824 18.4619L24.2386 21.7181C24.8473 22.3209 24.8473 22.9414 24.2445 23.5678ZM15.1319 6.66045C16.6802 6.92048 18.0867 7.65326 19.2095 8.77609C20.3323 9.89891 21.0592 11.3054 21.3251 12.8537C21.3902 13.2437 21.727 13.5156 22.1111 13.5156C22.1584 13.5156 22.1998 13.5097 22.247 13.5038C22.6844 13.4328 22.9739 13.0192 22.903 12.5819C22.5839 10.7085 21.6975 9.00065 20.3442 7.64736C18.9909 6.29406 17.283 5.40762 15.4096 5.0885C14.9723 5.01759 14.5646 5.30716 14.4877 5.73856C14.4109 6.16996 14.6946 6.58954 15.1319 6.66045ZM27.9675 12.3514C27.4416 9.26658 25.9878 6.45953 23.754 4.2257C21.5202 1.99188 18.7131 0.538116 15.6283 0.0121628C15.1969 -0.0646619 14.7891 0.230818 14.7123 0.662218C14.6414 1.09953 14.931 1.50729 15.3683 1.58411C18.1221 2.05097 20.6337 3.35699 22.6312 5.34852C24.6286 7.34597 25.9287 9.85754 26.3956 12.6114C26.4606 13.0014 26.7974 13.2733 27.1816 13.2733C27.2288 13.2733 27.2702 13.2674 27.3175 13.2615C27.7489 13.1965 28.0444 12.7828 27.9675 12.3514Z"
                                                fill="#F84E1D" />
                                        </svg>
                                    </div>
                                </li>
                                <li>
                                    <div class="content">
                                        <a href="tel:+918016358033">+91 8016358033</a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="footer-widget">
                        <div class="widget-title">
                            <h4>Location</h4>
                        </div>
                        <div class="location-information">
                            <ul>
                                <li>
                                    <div class="icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M16.0067 15.8571C18.5606 11.8496 18.2395 12.3496 18.3131 12.2451C19.2429 10.9336 19.7344 9.39009 19.7344 7.78125C19.7344 3.51469 16.2721 0 12 0C7.74178 0 4.26562 3.50775 4.26562 7.78125C4.26562 9.38906 4.76737 10.973 5.72766 12.3022L7.99322 15.8572C5.57095 16.2294 1.45312 17.3387 1.45312 19.7812C1.45312 20.6716 2.03428 21.9405 4.80291 22.9293C6.73612 23.6197 9.29208 24 12 24C17.0637 24 22.5469 22.5716 22.5469 19.7812C22.5469 17.3383 18.4339 16.2301 16.0067 15.8571ZM6.9023 11.5287C6.89457 11.5166 6.88649 11.5047 6.87806 11.4931C6.07898 10.3938 5.67188 9.09098 5.67188 7.78125C5.67188 4.26478 8.50341 1.40625 12 1.40625C15.4893 1.40625 18.3281 4.26605 18.3281 7.78125C18.3281 9.09309 17.9287 10.3517 17.1728 11.4221C17.1051 11.5114 17.4585 10.9624 12 19.5276L6.9023 11.5287ZM12 22.5938C6.46903 22.5938 2.85938 20.968 2.85938 19.7812C2.85938 18.9836 4.71413 17.6721 8.82413 17.1609L11.407 21.2138C11.4705 21.3135 11.5582 21.3956 11.6618 21.4525C11.7654 21.5093 11.8817 21.5391 12 21.5391C12.1182 21.5391 12.2345 21.5093 12.3381 21.4525C12.4417 21.3956 12.5294 21.3135 12.5929 21.2138L15.1757 17.1609C19.2858 17.6721 21.1406 18.9836 21.1406 19.7812C21.1406 20.9579 17.5635 22.5938 12 22.5938Z"
                                                fill="white" />
                                            <path
                                                d="M12 4.26562C10.0615 4.26562 8.48438 5.84273 8.48438 7.78125C8.48438 9.71977 10.0615 11.2969 12 11.2969C13.9385 11.2969 15.5156 9.71977 15.5156 7.78125C15.5156 5.84273 13.9385 4.26562 12 4.26562ZM12 9.89062C10.8369 9.89062 9.89062 8.94436 9.89062 7.78125C9.89062 6.61814 10.8369 5.67188 12 5.67188C13.1631 5.67188 14.1094 6.61814 14.1094 7.78125C14.1094 8.94436 13.1631 9.89062 12 9.89062Z"
                                                fill="white" />
                                        </svg>
                                    </div>
                                </li>
                                <li>
                                    <div class="content two">
                                        <a
                                            href="https://www.google.com/maps/place/VIP+Nagar,+Kolkata,+West+Bengal+700100/@22.5726,88.3639,12z">Bus Stop, 26VIP nagar, Eastern Metropolitan Bypass, near VIP bazar, Sector 1, VIP Nagar, Kolkata, West Bengal 700100</a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="social-area">
                            <span>Follow us</span>
                            <ul class="social-list">
                                <li><a href="https://www.facebook.com/">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6.92934 12V6.52664H8.76578L9.0413 4.39294H6.92934V3.03088C6.92934 2.41332 7.10013 1.99246 7.98671 1.99246L9.11564 1.99199V0.083538C8.92041 0.0581672 8.25025 0 7.47025 0C5.84149 0 4.72641 0.994179 4.72641 2.81956V4.39294H2.8844V6.52664H4.72641V12H6.92934Z"
                                                fill="#021118" />
                                        </svg>

                                    </a></li>
                                <li><a href="https://x.com/">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M7.1171 5.08118L11.4883 0H10.4525L6.65695 4.41192L3.62547 0H0.129028L4.71321 6.6716L0.129028 12H1.16492L5.1731 7.34086L8.37455 12H11.871L7.11684 5.08118H7.1171ZM5.69829 6.73038L5.23382 6.06604L1.53817 0.779804H3.12925L6.11168 5.04596L6.57615 5.71031L10.453 11.2557H8.86188L5.69829 6.73063V6.73038Z"
                                                fill="#021118" />
                                        </svg>
                                    </a></li>
                                <li><a href="https://www.linkedin.com/">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M11.997 12L12 11.9995V7.5985C12 5.4455 11.5365 3.787 9.0195 3.787C7.8095 3.787 6.9975 4.451 6.666 5.0805H6.631V3.988H4.2445V11.9995H6.7295V8.0325C6.7295 6.988 6.9275 5.978 8.221 5.978C9.4955 5.978 9.5145 7.17 9.5145 8.0995V12H11.997ZM0.198 3.9885H2.686V12H0.198V3.9885ZM1.441 0C0.6455 0 0 0.6455 0 1.441C0 2.2365 0.6455 2.8955 1.441 2.8955C2.2365 2.8955 2.882 2.2365 2.882 1.441C2.88174 1.0589 2.72983 0.692534 2.45965 0.422352C2.18947 0.152169 1.8231 0.000264884 1.441 0Z"
                                                fill="#021118" />
                                        </svg>

                                    </a></li>
                                <li><a href="https://www.instagram.com/">
                                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M9.00014 0H3.00029C1.35048 0 0.000366211 1.35011 0.000366211 2.99993V9.00007C0.000366211 10.6494 1.35048 12 3.00029 12H9.00014C10.65 12 12.0001 10.6494 12.0001 9.00007V2.99993C12.0001 1.35011 10.65 0 9.00014 0ZM11 9.00007C11 10.1025 10.1032 11 9.00014 11H3.00029C1.89767 11 1.00039 10.1025 1.00039 9.00007V2.99993C1.00039 1.89716 1.89767 1.00002 3.00029 1.00002H9.00014C10.1032 1.00002 11 1.89716 11 2.99993V9.00007Z"
                                                fill="#021118" />
                                            <path
                                                d="M9.25041 3.49996C9.66461 3.49996 10.0004 3.16419 10.0004 2.74998C10.0004 2.33578 9.66461 2 9.25041 2C8.83621 2 8.50043 2.33578 8.50043 2.74998C8.50043 3.16419 8.83621 3.49996 9.25041 3.49996Z"
                                                fill="#021118" />
                                            <path
                                                d="M5.99999 3C4.34282 3 3.00006 4.34291 3.00006 5.99993C3.00006 7.65633 4.34282 9.00015 5.99999 9.00015C7.65669 9.00015 8.99991 7.65633 8.99991 5.99993C8.99991 4.34291 7.65669 3 5.99999 3ZM5.99999 8.00012C4.89555 8.00012 4.00009 7.10466 4.00009 5.99993C4.00009 4.8952 4.89555 4.00002 5.99999 4.00002C7.10442 4.00002 7.99989 4.8952 7.99989 5.99993C7.99989 7.10466 7.10442 8.00012 5.99999 8.00012Z"
                                                fill="#021118" />
                                        </svg>

                                    </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom-wrapper">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="footer-bottom">
                            <div class="copy-write-area">
                                <p>©Copyright 2025 GPS Service Pvt. Ltd.</a></p>
                            </div>
                            <div class="middle-logo">
                                <a href="#">
                                    <img src="assets/img/logo.png" alt="">
                                </a>

                            </div>
                            <div class="footer-right">
                                <ul>
                                    <li><a href="#">Terms & Conditions</a></li>
                                    <li><a href="#">Privacy Policy</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- footer section end  -->

    <!--  Main jQuery  -->
    <script data-cfasync="false" src="../../../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.7.1.min.js"></script>
    <!-- Popper and Bootstrap JS -->
    <script src="assets/js/popper.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <!-- Swiper slider JS -->
    <!-- smooth scroll js sart -->
    <script src="assets/js/ScrollSmoother.min.js"></script>
    <script src="assets/js/splite-type.min.js"></script>
    <!-- smooth scroll js end -->
    <!-- Counter JS -->
    <script src="assets/js/jquery.counterup.min.js"></script>
    <script src="assets/js/waypoints.min.js"></script>
    <!-- marquee js -->
    <script src="assets/js/jquery.marquee.min.js"></script>
    <!-- swiper -->
    <script src="assets/js/swiper-bundle.min.js"></script>
    <script src="assets/js/jquery.fancybox.min.js"></script>
    <script src="assets/js/jquery.nice-select.min.js"></script>
    <!-- Wow  JS -->
    <script src="assets/js/wow.min.js"></script>
    <!-- gsp  JS -->
    <script src="assets/js/gsap.min.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- Enquiry Modal -->
    <div class="modal fade enquiry-modal-wrapper" id="enquiryModal" tabindex="-1" aria-labelledby="enquiryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header border-0 pb-0">
                    <h5 class="modal-title fw-bold" id="enquiryModalLabel">Service Enquiry Form</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="enquiryForm">
                        <!-- Service Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Service</h6>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="serviceType" class="form-label fw-semibold">Which Service Do You Need?</label>
                                <select class="form-select" id="serviceType" required>
                                    <option value="">Select Service</option>
                                    <option value="security-guard">Security Guard</option>
                                    <option value="bodyguard">Bodyguard</option>
                                    <option value="bouncer">Bouncer</option>
                                    <option value="receptionist">Receptionist</option>
                                    <option value="housekeeping">Housekeeping</option>
                                    <option value="facility-management">Facility Management</option>
                                    <option value="event-security">Event Security</option>
                                    <option value="valet-services">Valet Services</option>
                                    <option value="office-peon">Office Peon</option>
                                    <option value="computer-operator">Computer Operator</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="serviceLocation" class="form-label fw-semibold">Service Location</label>
                                <input type="text" class="form-control" id="serviceLocation" placeholder="E.g. Kolkata" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="serviceDate" class="form-label fw-semibold">Service Required From</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                                    <input type="date" class="form-control" id="serviceDate" required>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="serviceDuration" class="form-label fw-semibold">Service Duration</label>
                                <select class="form-select" id="serviceDuration">
                                    <option value="">Select Duration</option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                    <option value="event-based">Event Based</option>
                                </select>
                            </div>
                        </div>

                        <!-- Personnel Count Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Personnel Count</h6>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="malePersons" class="form-label fw-semibold">Male</label>
                                <input type="number" class="form-control" id="malePersons" min="0" value="0" onchange="calculateTotal()">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="femalePersons" class="form-label fw-semibold">Female</label>
                                <input type="number" class="form-control" id="femalePersons" min="0" value="0" onchange="calculateTotal()">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="totalPersons" class="form-label fw-semibold">Total</label>
                                <input type="number" class="form-control" id="totalPersons" readonly>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="workingHours" class="form-label fw-semibold">Working Hours</label>
                                <select class="form-select" id="workingHours">
                                    <option value="">Select Hours</option>
                                    <option value="8">8 Hours</option>
                                    <option value="12">12 Hours</option>
                                    <option value="24">24 Hours</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                        </div>

                        <!-- Add Item Button -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary btn-sm" onclick="addServiceItem()">Add Item</button>
                            </div>
                        </div>

                        <!-- Additional Service Items Container -->
                        <div id="additionalServiceItems"></div>

                        <!-- Company Information Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Company Information</h6>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="companyName" class="form-label fw-semibold">Company Name</label>
                                <input type="text" class="form-control" id="companyName" placeholder="E.g. ABC Company Ltd." required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="companyAddress" class="form-label fw-semibold">Company Address</label>
                                <input type="text" class="form-control" id="companyAddress" placeholder="E.g. 123 Business Street" required>
                            </div>
                        </div>

                        <!-- GST Details Section -->
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="gstStatus" class="form-label fw-semibold">GST Status</label>
                                <select class="form-select w-100" id="gstStatus">
                                    <option value="">Yes</option>
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="gstNumber" class="form-label fw-semibold">GST Number</label>
                                <input type="text" class="form-control w-100" id="gstNumber">
                                <small class="form-text text-muted">If you have</small>
                            </div>
                        </div>

                        <!-- Contact Details Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">Contact Details</h6>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="contactName" class="form-label fw-semibold">Name</label>
                                <input type="text" class="form-control" id="contactName" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="designation" class="form-label fw-semibold">Designation</label>
                                <input type="text" class="form-control" id="designation" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="contactNumber" class="form-label fw-semibold">Contact Number</label>
                                <input type="tel" class="form-control" id="contactNumber" placeholder="E.g. ****** 400 5000" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="emailAddress" class="form-label fw-semibold">Email Address</label>
                                <input type="email" class="form-control" id="emailAddress" required>
                            </div>
                        </div>

                        <!-- Add Item Button for Contact -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary btn-sm" onclick="addContactItem()">Add Item</button>
                            </div>
                        </div>

                        <!-- Additional Contact Items Container -->
                        <div id="additionalContactItems"></div>

                        <!-- Visiting Card Upload and Appointment Schedule Section -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-3">
                                <label for="visitingCard" class="form-label fw-semibold">Upload Visiting Card</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('visitingCardFile').click()">
                                        Choose File
                                    </button>
                                    <input type="file" id="visitingCardFile" class="d-none" accept=".jpg,.png,.pdf">
                                    <small class="form-text text-muted mt-1">No file chosen (JPG, PNG, PDF)</small>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="appointmentDate" class="form-label fw-semibold">Appointment Date</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                                    <input type="date" class="form-control" id="appointmentDate">
                                </div>
                                <small class="form-text text-muted">When would you like to schedule a meeting?</small>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="fromTime" class="form-label fw-semibold">from (Time)</label>
                                <select class="form-select w-100" id="fromTime">
                                    <option value="">Select Time</option>
                                    <option value="9">9:00 AM</option>
                                    <option value="10">10:00 AM</option>
                                    <option value="11">11:00 AM</option>
                                    <option value="12">12:00 PM</option>
                                    <option value="13">1:00 PM</option>
                                    <option value="14">2:00 PM</option>
                                    <option value="15">3:00 PM</option>
                                    <option value="16">4:00 PM</option>
                                    <option value="17">5:00 PM</option>
                                    <option value="18">6:00 PM</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="toTime" class="form-label fw-semibold">To (Time)</label>
                                <select class="form-select w-100" id="toTime">
                                    <option value="">Select Time</option>
                                    <option value="10">10:00 AM</option>
                                    <option value="11">11:00 AM</option>
                                    <option value="12">12:00 PM</option>
                                    <option value="13">1:00 PM</option>
                                    <option value="14">2:00 PM</option>
                                    <option value="15">3:00 PM</option>
                                    <option value="16">4:00 PM</option>
                                    <option value="17">5:00 PM</option>
                                    <option value="18">6:00 PM</option>
                                    <option value="19">7:00 PM</option>
                                    <option value="20">8:00 PM</option>
                                </select>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg px-5">Submit Enquiry</button>
                            </div>
                        </div>


                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Application Modal -->
    <div class="modal fade enquiry-modal-wrapper" id="jobApplicationModal" tabindex="-1" aria-labelledby="jobApplicationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header border-0 pb-0">
                    <h5 class="modal-title fw-bold" id="jobApplicationModalLabel">Job Application Form</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="jobApplicationForm">
                        <!-- Row 1: Job Role, Full Name, Aadhar Number, Father Name -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <label for="jobRole" class="form-label">Select a Job Role</label>
                                <select class="form-select" id="jobRole" required>
                                    <option value="security-guard">Security Guard</option>
                                    <option value="bodyguard">Bodyguard</option>
                                    <option value="bouncer">Bouncer</option>
                                    <option value="receptionist">Receptionist</option>
                                    <option value="housekeeping">Housekeeping Staff</option>
                                    <option value="facility-management">Facility Management</option>
                                    <option value="event-security">Event Security</option>
                                    <option value="valet-services">Valet Services</option>
                                    <option value="office-peon">Office Peon</option>
                                    <option value="computer-operator">Computer Operator</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobFullName" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="jobFullName" placeholder="E.g. John Doe" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobAadharNumber" class="form-label">Aadhar Number</label>
                                <input type="text" class="form-control" id="jobAadharNumber" placeholder="**************" maxlength="14">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobFatherName" class="form-label">Father Name</label>
                                <input type="text" class="form-control" id="jobFatherName">
                            </div>
                        </div>

                        <!-- Row 2: Mother Name, Marital Status, DOB, Gender -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <label for="jobMotherName" class="form-label">Mother Name</label>
                                <input type="text" class="form-control" id="jobMotherName">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobMaritalStatus" class="form-label">Marital Status</label>
                                <select class="form-select" id="jobMaritalStatus">
                                    <option value="single">Single</option>
                                    <option value="married">Married</option>
                                    <option value="divorced">Divorced</option>
                                    <option value="widowed">Widowed</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobDob" class="form-label">DOB</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                                    <input type="date" class="form-control" id="jobDob" placeholder="Choose Date">
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobGender" class="form-label">Gender</label>
                                <select class="form-select" id="jobGender">
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>

                        <!-- Row 3: Blood Group, Village/House No., Post Office, Block/Municipality -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <label for="jobBloodGroup" class="form-label">Blood Group</label>
                                <select class="form-select" id="jobBloodGroup">
                                    <option value="A+">A+</option>
                                    <option value="A-">A-</option>
                                    <option value="B+">B+</option>
                                    <option value="B-">B-</option>
                                    <option value="AB+">AB+</option>
                                    <option value="AB-">AB-</option>
                                    <option value="O+">O+</option>
                                    <option value="O-">O-</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobVillageHouseNo" class="form-label">Village/House No.</label>
                                <input type="text" class="form-control" id="jobVillageHouseNo">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobPostOffice" class="form-label">Post Office</label>
                                <input type="text" class="form-control" id="jobPostOffice">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobBlockMunicipality" class="form-label">Block / Municipality</label>
                                <input type="text" class="form-control" id="jobBlockMunicipality">
                            </div>
                        </div>

                        <!-- Row 4: Police Station, State, District, Zip Code -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <label for="jobPoliceStation" class="form-label">Police Station</label>
                                <input type="text" class="form-control" id="jobPoliceStation">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobState" class="form-label">State</label>
                                <select class="form-select" id="jobState">
                                    <option value="">state</option>
                                    <option value="west-bengal">West Bengal</option>
                                    <option value="bihar">Bihar</option>
                                    <option value="jharkhand">Jharkhand</option>
                                    <option value="odisha">Odisha</option>
                                    <option value="assam">Assam</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobDistrict" class="form-label">District</label>
                                <input type="text" class="form-control" id="jobDistrict">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobZipCode" class="form-label">Zip Code</label>
                                <input type="text" class="form-control" id="jobZipCode" placeholder="E.g. 10">
                            </div>
                        </div>

                        <!-- Row 5: Religion, Height, Weight, Identification Marks -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <label for="jobReligion" class="form-label">Religion</label>
                                <select class="form-select" id="jobReligion">
                                    <option value="hinduism">Hinduism</option>
                                    <option value="islam">Islam</option>
                                    <option value="christianity">Christianity</option>
                                    <option value="sikhism">Sikhism</option>
                                    <option value="buddhism">Buddhism</option>
                                    <option value="jainism">Jainism</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobHeight" class="form-label">Height in cm</label>
                                <input type="number" class="form-control" id="jobHeight" placeholder="E.g. 10">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobWeight" class="form-label">Weight in Kg</label>
                                <input type="number" class="form-control" id="jobWeight" placeholder="E.g. 10">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobIdentificationMarks" class="form-label">Identification Marks</label>
                                <input type="text" class="form-control" id="jobIdentificationMarks">
                            </div>
                        </div>

                        <!-- Row 6: Contact Information -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <label for="jobPhoneNumber" class="form-label">Number</label>
                                <input type="tel" class="form-control" id="jobPhoneNumber" placeholder="E.g. ****** 400 5000">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobAlternativeNumber" class="form-label">Alternative Number</label>
                                <input type="tel" class="form-control" id="jobAlternativeNumber" placeholder="E.g. ****** 400 5000">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="jobEmailAddress" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="jobEmailAddress" placeholder="E.g. <EMAIL>">
                            </div>
                            <div class="col-md-3 mb-3">
                                <!-- Empty column for consistent spacing -->
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg px-4">Submit Application</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let serviceItemCount = 0;
        let contactItemCount = 0;

        // Auto-calculate total persons
        document.getElementById('malePersons').addEventListener('input', calculateTotal);
        document.getElementById('femalePersons').addEventListener('input', calculateTotal);

        function calculateTotal() {
            const male = parseInt(document.getElementById('malePersons').value) || 0;
            const female = parseInt(document.getElementById('femalePersons').value) || 0;
            document.getElementById('totalPersons').value = male + female;
        }

        // Add Service Item functionality
        function addServiceItem() {
            serviceItemCount++;
            const container = document.getElementById('additionalServiceItems');
            const newItem = document.createElement('div');
            newItem.className = 'row mb-4 additional-service-item';
            newItem.id = `serviceItem${serviceItemCount}`;

            newItem.innerHTML = `
                <div class="col-12 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="fw-bold mb-0">Additional Service ${serviceItemCount}</h6>
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeServiceItem(${serviceItemCount})">
                            <i class="bi bi-trash"></i> Remove
                        </button>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="serviceType${serviceItemCount}" class="form-label fw-semibold">What Service Do You Need?</label>
                    <select class="form-select w-100" id="serviceType${serviceItemCount}" name="additionalServiceType[]">
                        <option value="">Select Service</option>
                        <option value="security-guard">Security Guard</option>
                        <option value="bodyguard">Bodyguard</option>
                        <option value="bouncer">Bouncer</option>
                        <option value="receptionist">Receptionist</option>
                        <option value="housekeeping">Housekeeping</option>
                        <option value="facility-management">Facility Management</option>
                        <option value="event-security">Event Security</option>
                        <option value="valet-services">Valet Services</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="servicePurpose${serviceItemCount}" class="form-label fw-semibold">Service Purpose</label>
                    <select class="form-select w-100" id="servicePurpose${serviceItemCount}" name="additionalServicePurpose[]">
                        <option value="">Select Purpose</option>
                        <option value="regular">Regular</option>
                        <option value="temporary">Temporary</option>
                        <option value="event-based">Event Based</option>
                        <option value="emergency">Emergency</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="startDate${serviceItemCount}" class="form-label fw-semibold">Start Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                        <input type="date" class="form-control" id="startDate${serviceItemCount}" name="additionalStartDate[]">
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="endDate${serviceItemCount}" class="form-label fw-semibold">End Date</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                        <input type="date" class="form-control" id="endDate${serviceItemCount}" name="additionalEndDate[]">
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="malePersons${serviceItemCount}" class="form-label fw-semibold">How Many Male Person</label>
                    <input type="number" class="form-control w-100" id="malePersons${serviceItemCount}" name="additionalMalePersons[]" placeholder="E.g. 10" min="0" onchange="calculateAdditionalTotal(${serviceItemCount})">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="femalePersons${serviceItemCount}" class="form-label fw-semibold">How Many Female Person</label>
                    <input type="number" class="form-control w-100" id="femalePersons${serviceItemCount}" name="additionalFemalePersons[]" placeholder="E.g. 10" min="0" onchange="calculateAdditionalTotal(${serviceItemCount})">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="totalPersons${serviceItemCount}" class="form-label fw-semibold">Total</label>
                    <input type="number" class="form-control w-100" id="totalPersons${serviceItemCount}" name="additionalTotalPersons[]" placeholder="0" readonly>
                </div>
            `;

            container.appendChild(newItem);
        }

        // Remove Service Item functionality
        function removeServiceItem(itemId) {
            const item = document.getElementById(`serviceItem${itemId}`);
            if (item) {
                item.remove();
            }
        }

        // Calculate total for additional service items
        function calculateAdditionalTotal(itemId) {
            const male = parseInt(document.getElementById(`malePersons${itemId}`).value) || 0;
            const female = parseInt(document.getElementById(`femalePersons${itemId}`).value) || 0;
            document.getElementById(`totalPersons${itemId}`).value = male + female;
        }

        // Add Contact Item functionality
        function addContactItem() {
            contactItemCount++;
            const container = document.getElementById('additionalContactItems');
            const newItem = document.createElement('div');
            newItem.className = 'row mb-4 additional-contact-item';
            newItem.id = `contactItem${contactItemCount}`;

            newItem.innerHTML = `
                <div class="col-12 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="fw-bold mb-0">Additional Contact ${contactItemCount}</h6>
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeContactItem(${contactItemCount})">
                            <i class="bi bi-trash"></i> Remove
                        </button>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="contactName${contactItemCount}" class="form-label fw-semibold">Name</label>
                    <input type="text" class="form-control w-100" id="contactName${contactItemCount}" name="additionalContactName[]">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="designation${contactItemCount}" class="form-label fw-semibold">Designation</label>
                    <input type="text" class="form-control w-100" id="designation${contactItemCount}" name="additionalDesignation[]">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="contactNumber${contactItemCount}" class="form-label fw-semibold">Contact Number</label>
                    <input type="tel" class="form-control w-100" id="contactNumber${contactItemCount}" name="additionalContactNumber[]" placeholder="E.g. ****** 400 5000">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="emailAddress${contactItemCount}" class="form-label fw-semibold">Email Address</label>
                    <input type="email" class="form-control w-100" id="emailAddress${contactItemCount}" name="additionalEmailAddress[]">
                </div>
            `;

            container.appendChild(newItem);
        }

        // Remove Contact Item functionality
        function removeContactItem(itemId) {
            const item = document.getElementById(`contactItem${itemId}`);
            if (item) {
                item.remove();
            }
        }

        // File upload handler
        document.getElementById('resumeFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file chosen (PDF, DOC, DOCX)';
            e.target.parentElement.querySelector('small').textContent = fileName;
        });

        // File upload handlers
        document.getElementById('paySlipFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file chosen';
            e.target.parentElement.querySelector('small').textContent = fileName;
        });

        document.getElementById('photoFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file chosen';
            e.target.parentElement.querySelector('small').textContent = fileName;
        });

        document.getElementById('signatureFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file chosen';
            e.target.parentElement.querySelector('small').textContent = fileName;
        });

        // Form submission handler
        document.getElementById('jobApplicationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Collect form data
            const formData = new FormData(this);

            // Here you can add your form submission logic
            alert('Thank you for your job application! We will review your application and contact you soon.');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('enquiryModal'));
            modal.hide();

            // Reset form
            this.reset();

            // Reset file upload displays
            const paySlipText = document.querySelector('#paySlipFile').parentElement.querySelector('small');
            if (paySlipText) paySlipText.textContent = 'No file chosen';

            const photoText = document.querySelector('#photoFile').parentElement.querySelector('small');
            if (photoText) photoText.textContent = 'No file chosen';

            const signatureText = document.querySelector('#signatureFile').parentElement.querySelector('small');
            if (signatureText) signatureText.textContent = 'No file chosen';
        });
    </script>
</body>



</html>